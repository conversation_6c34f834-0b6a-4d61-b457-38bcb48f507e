from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from models.douyin import CommentInfo


class CommentCustomerAnalysisRequest(BaseModel):
    """评论客户分析请求模型"""
    comments: List[CommentInfo] = Field(..., description="评论列表", min_length=1)
    group_by_user: bool = Field(default=True, description="是否按用户分组分析，默认为True")
    min_confidence: float = Field(default=0.6, description="最小置信度阈值", ge=0.0, le=1.0)
    timeout_seconds: int = Field(default=2400, description="请求超时时间（秒）", ge=5, le=3600)
    max_concurrent_requests: int = Field(default=15, description="最大并发请求数", ge=1, le=50)


class CustomerIntent(BaseModel):
    """客户意向分析结果"""
    user_nickname: str = Field(..., description="用户昵称")
    user_uid: str = Field(..., description="用户UID")
    user_short_id: str = Field(default="", description="用户抖音号")
    user_sec_uid: str = Field(default="", description="用户安全UID")
    intent_type: str = Field(..., description="意向类型：租赁/购买/待定")
    confidence: float = Field(..., description="置信度", ge=0.0, le=1.0)
    reasoning: str = Field(..., description="判断理由，引用评论中的关键词或短语")
    comments_analyzed: List[str] = Field(..., description="分析的评论内容列表")
    comment_count: int = Field(..., description="该用户的评论数量")
    video_ids: List[str] = Field(default_factory=list, description="相关视频ID列表")
    video_links: List[str] = Field(default_factory=list, description="相关视频链接列表")
    comment_times: List[str] = Field(default_factory=list, description="评论时间列表（格式化后）")
    ip_labels: List[str] = Field(default_factory=list, description="评论IP地址标签列表")
    latest_comment_time: str = Field(default="", description="最新评论时间")


class CommentCustomerAnalysisResult(BaseModel):
    """评论客户分析结果"""
    potential_customers: List[CustomerIntent] = Field(default_factory=list, description="潜在客户列表")
    total_users_analyzed: int = Field(..., description="分析的用户总数")
    total_comments_analyzed: int = Field(..., description="分析的评论总数")
    processing_time: float = Field(..., description="处理时间（秒）")
    model_used: str = Field(..., description="使用的AI模型")
    analysis_summary: str = Field(..., description="分析摘要")

    model_config = {"protected_namespaces": ()}
