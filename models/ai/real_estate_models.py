from typing import Optional, List, Dict
from pydantic import BaseModel, Field


class RealEstateAnalysisRequest(BaseModel):
    admin_password: str
    timeout_seconds: int = Field(default=60, ge=10, le=300)


class RealEstateAnalysisResult(BaseModel):
    aweme_id: str
    video_description: str
    is_real_estate_related: bool
    confidence_score: float = Field(ge=0.0, le=1.0)
    analysis_reasoning: str
    keywords_found: List[str] = Field(default_factory=list)
    category: Optional[str] = None
    intent_type: Optional[str] = None


class BatchAnalysisResponse(BaseModel):
    total_analyzed: int
    real_estate_count: int
    non_real_estate_count: int
    results: List[RealEstateAnalysisResult]
    processing_time: float
    success_rate: float = Field(ge=0.0, le=1.0)


class SingleAnalysisResponse(BaseModel):
    result: RealEstateAnalysisResult
    processing_time: float
    model_used: str


class AnalysisStatsRequest(BaseModel):
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    category_filter: Optional[str] = None


class AnalysisStatsResponse(BaseModel):
    total_videos: int
    analyzed_videos: int
    real_estate_videos: int
    real_estate_percentage: float
    category_distribution: Dict[str, int] = Field(default_factory=dict)
    intent_distribution: Dict[str, int] = Field(default_factory=dict)
    average_confidence: float
    analysis_date_range: Dict[str, str] = Field(default_factory=dict)
