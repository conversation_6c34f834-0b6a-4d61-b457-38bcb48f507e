import pymysql
from datetime import datetime
from typing import List, Optional
from contextlib import contextmanager

from config.settings import settings
from database.douyin.models import CustomerAnalysisResult
from core.logging_config import get_logger

logger = get_logger(__name__)


class SignatureAnalysisDatabase:
    """签名分析数据库服务"""
    
    def __init__(self):
        self.host = settings.MYSQL_HOST
        self.port = settings.MYSQL_PORT
        self.user = settings.MYSQL_USER
        self.password = settings.MYSQL_PASSWORD
        self.database = settings.MYSQL_DATABASE
        self.create_table_if_not_exists()

    @contextmanager
    def _get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4',
                autocommit=False
            )
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"数据库连接错误: {e}")
            raise
        finally:
            if connection:
                connection.close()

    def create_table_if_not_exists(self):
        """创建客户分析结果表"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    create_sql = """
                        CREATE TABLE IF NOT EXISTS customer_analysis_results (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            sec_uid VARCHAR(255) NOT NULL,
                            user_nickname VARCHAR(255) NOT NULL,
                            signature TEXT,
                            status INT NOT NULL DEFAULT 0 COMMENT '0=待处理, 1=非厂房销售, 2=厂房销售',
                            confidence DECIMAL(3,2) DEFAULT NULL COMMENT '分析置信度',
                            reasoning TEXT DEFAULT NULL COMMENT '分析理由',
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            INDEX idx_sec_uid (sec_uid),
                            INDEX idx_status (status),
                            INDEX idx_created_at (created_at)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户分析结果表'
                    """
                    cursor.execute(create_sql)
                    conn.commit()
                    logger.info("客户分析结果表创建/检查完成")
        except Exception as e:
            logger.error(f"创建客户分析结果表失败: {e}")
            raise

    def get_pending_signatures(self, limit: int = 100) -> List[CustomerAnalysisResult]:
        """获取待处理的签名数据（status='0'）"""
        try:
            with self._get_connection() as conn:
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                    sql = """
                        SELECT id, signature, status
                        FROM customer_analysis_results
                        WHERE status = '0'
                        ORDER BY created_at ASC
                        LIMIT %s
                    """
                    cursor.execute(sql, (limit,))
                    results = cursor.fetchall()
                    return [CustomerAnalysisResult(**row) for row in results]
        except Exception as e:
            logger.error(f"获取待处理签名数据失败: {e}")
            return []

    def update_signature_status(self, record_id: int, status: str) -> bool:
        """更新签名分析状态"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    sql = """
                        UPDATE customer_analysis_results
                        SET status = %s
                        WHERE id = %s
                    """
                    cursor.execute(sql, (status, record_id))
                    conn.commit()
                    return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"更新签名状态失败: {e}")
            return False

    def batch_update_status(self, updates: List[dict]) -> int:
        """批量更新签名状态"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    sql = """
                        UPDATE customer_analysis_results
                        SET status = %s
                        WHERE id = %s
                    """
                    update_data = [
                        (update['status'], update['id'])
                        for update in updates
                    ]
                    cursor.executemany(sql, update_data)
                    conn.commit()
                    return cursor.rowcount
        except Exception as e:
            logger.error(f"批量更新签名状态失败: {e}")
            return 0

    def get_status_statistics(self) -> dict:
        """获取状态统计信息"""
        try:
            with self._get_connection() as conn:
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                    sql = """
                        SELECT 
                            status,
                            COUNT(*) as count,
                            COUNT(CASE WHEN signature IS NOT NULL AND signature != '' THEN 1 END) as has_signature_count
                        FROM customer_analysis_results 
                        GROUP BY status
                    """
                    cursor.execute(sql)
                    results = cursor.fetchall()
                    
                    stats = {
                        'pending': 0,      # status=0
                        'non_seller': 0,   # status=1  
                        'seller': 0,       # status=2
                        'total': 0
                    }
                    
                    for row in results:
                        stats['total'] += row['count']
                        if row['status'] == '0':
                            stats['pending'] = row['count']
                        elif row['status'] == '1':
                            stats['non_seller'] = row['count']
                        elif row['status'] == '2':
                            stats['seller'] = row['count']
                    
                    return stats
        except Exception as e:
            logger.error(f"获取状态统计失败: {e}")
            return {'pending': 0, 'non_seller': 0, 'seller': 0, 'total': 0}


# 创建全局实例
signature_analysis_db = SignatureAnalysisDatabase()
