from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class VideoData(BaseModel):
    """视频数据模型"""
    aweme_id: str  # 对应video_id字段
    video_title_description: Optional[str] = None
    author_nickname: Optional[str] = None
    comment_count: Optional[int] = None  # 评论数量
    create_time: Optional[datetime] = None  # 对应publish_time字段
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class VideoAnalysisResult(BaseModel):
    """视频分析结果模型"""
    id: Optional[int] = None
    aweme_id: str
    video_title_description: str
    is_real_estate_related: bool
    confidence_score: float
    analysis_result: str
    keywords_found: Optional[str] = None
    analysis_time: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class TaskCostRecord(BaseModel):
    """AI任务成本记录模型"""
    id: Optional[int] = None
    task_type: str  # 任务类型：customer_analysis, real_estate_analysis, topic_analysis
    model_name: str  # 使用的AI模型名称
    input_tokens: int  # 输入token数量
    output_tokens: int  # 输出token数量
    total_tokens: int  # 总token数量
    input_cost: float  # 输入成本（人民币）
    output_cost: float  # 输出成本（人民币）
    total_cost: float  # 总成本（人民币）
    task_data_count: int  # 处理的数据量（如评论数、视频数等）
    processing_time: float  # 处理时间（秒）
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True
