import pymysql
from contextlib import contextmanager
from typing import List, Optional
from datetime import datetime

from config.settings import settings
from database.ai import TaskCostRecord


class TaskCostDatabase:
    """AI任务成本记录数据库服务"""

    def __init__(self):
        self.connection_config = {
            'host': settings.MYSQL_HOST,
            'port': settings.MYSQL_PORT,
            'user': settings.MYSQL_USER,
            'password': settings.MYSQL_PASSWORD,
            'database': settings.MYSQL_DATABASE,
            'charset': settings.MYSQL_CHARSET,
            'autocommit': True
        }
        self._test_connection()
        self.create_table_if_not_exists()

    def _test_connection(self):
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
        except Exception:
            raise
    
    @contextmanager
    def _get_connection(self):
        connection = None
        try:
            connection = pymysql.connect(**self.connection_config)
            yield connection
        except Exception:
            raise
        finally:
            if connection:
                connection.close()

    def create_table_if_not_exists(self):
        """创建AI任务成本记录表"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    create_sql = """
                        CREATE TABLE IF NOT EXISTS ai_task_cost_records (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            task_type VARCHAR(50) NOT NULL COMMENT '任务类型',
                            model_name VARCHAR(100) NOT NULL COMMENT 'AI模型名称',
                            input_tokens INT NOT NULL DEFAULT 0 COMMENT '输入token数量',
                            output_tokens INT NOT NULL DEFAULT 0 COMMENT '输出token数量',
                            total_tokens INT NOT NULL DEFAULT 0 COMMENT '总token数量',
                            input_cost DECIMAL(10,6) NOT NULL DEFAULT 0.000000 COMMENT '输入成本（人民币）',
                            output_cost DECIMAL(10,6) NOT NULL DEFAULT 0.000000 COMMENT '输出成本（人民币）',
                            total_cost DECIMAL(10,6) NOT NULL DEFAULT 0.000000 COMMENT '总成本（人民币）',
                            task_data_count INT NOT NULL DEFAULT 0 COMMENT '处理的数据量',
                            processing_time DECIMAL(8,3) NOT NULL DEFAULT 0.000 COMMENT '处理时间（秒）',
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            INDEX idx_task_type (task_type),
                            INDEX idx_model_name (model_name),
                            INDEX idx_created_at (created_at),
                            INDEX idx_total_cost (total_cost)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI任务成本记录表'
                    """
                    cursor.execute(create_sql)
        except Exception:
            raise

    def save_task_cost(self, record: TaskCostRecord) -> bool:
        """保存任务成本记录"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    insert_sql = """
                        INSERT INTO ai_task_cost_records (
                            task_type, model_name, input_tokens, output_tokens, total_tokens,
                            input_cost, output_cost, total_cost, task_data_count, processing_time
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(insert_sql, (
                        record.task_type, record.model_name, record.input_tokens,
                        record.output_tokens, record.total_tokens, record.input_cost,
                        record.output_cost, record.total_cost, record.task_data_count,
                        record.processing_time
                    ))
                    return True
        except Exception:
            return False

    def get_cost_records(self, task_type: Optional[str] = None, limit: int = 100, offset: int = 0) -> List[TaskCostRecord]:
        """获取成本记录列表"""
        try:
            with self._get_connection() as conn:
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                    if task_type:
                        sql = """
                            SELECT * FROM ai_task_cost_records 
                            WHERE task_type = %s 
                            ORDER BY created_at DESC 
                            LIMIT %s OFFSET %s
                        """
                        cursor.execute(sql, (task_type, limit, offset))
                    else:
                        sql = """
                            SELECT * FROM ai_task_cost_records 
                            ORDER BY created_at DESC 
                            LIMIT %s OFFSET %s
                        """
                        cursor.execute(sql, (limit, offset))
                    
                    results = cursor.fetchall()
                    return [TaskCostRecord(**row) for row in results]
        except Exception:
            return []

    def get_cost_statistics(self, task_type: Optional[str] = None, days: int = 30) -> dict:
        """获取成本统计信息"""
        try:
            with self._get_connection() as conn:
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                    if task_type:
                        sql = """
                            SELECT 
                                COUNT(*) as total_tasks,
                                SUM(total_tokens) as total_tokens,
                                SUM(total_cost) as total_cost,
                                AVG(total_cost) as avg_cost,
                                MAX(total_cost) as max_cost,
                                MIN(total_cost) as min_cost
                            FROM ai_task_cost_records 
                            WHERE task_type = %s AND created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
                        """
                        cursor.execute(sql, (task_type, days))
                    else:
                        sql = """
                            SELECT 
                                COUNT(*) as total_tasks,
                                SUM(total_tokens) as total_tokens,
                                SUM(total_cost) as total_cost,
                                AVG(total_cost) as avg_cost,
                                MAX(total_cost) as max_cost,
                                MIN(total_cost) as min_cost
                            FROM ai_task_cost_records 
                            WHERE created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
                        """
                        cursor.execute(sql, (days,))
                    
                    result = cursor.fetchone()
                    if result:
                        return {
                            "total_tasks": result["total_tasks"] or 0,
                            "total_tokens": result["total_tokens"] or 0,
                            "total_cost": float(result["total_cost"] or 0),
                            "avg_cost": float(result["avg_cost"] or 0),
                            "max_cost": float(result["max_cost"] or 0),
                            "min_cost": float(result["min_cost"] or 0)
                        }
                    return {}
        except Exception:
            return {}

    def get_daily_cost_trend(self, task_type: Optional[str] = None, days: int = 7) -> List[dict]:
        """获取每日成本趋势"""
        try:
            with self._get_connection() as conn:
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                    if task_type:
                        sql = """
                            SELECT 
                                DATE(created_at) as date,
                                COUNT(*) as task_count,
                                SUM(total_tokens) as total_tokens,
                                SUM(total_cost) as total_cost
                            FROM ai_task_cost_records 
                            WHERE task_type = %s AND created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
                            GROUP BY DATE(created_at)
                            ORDER BY date DESC
                        """
                        cursor.execute(sql, (task_type, days))
                    else:
                        sql = """
                            SELECT 
                                DATE(created_at) as date,
                                COUNT(*) as task_count,
                                SUM(total_tokens) as total_tokens,
                                SUM(total_cost) as total_cost
                            FROM ai_task_cost_records 
                            WHERE created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
                            GROUP BY DATE(created_at)
                            ORDER BY date DESC
                        """
                        cursor.execute(sql, (days,))
                    
                    results = cursor.fetchall()
                    return [
                        {
                            "date": str(row["date"]),
                            "task_count": row["task_count"],
                            "total_tokens": row["total_tokens"] or 0,
                            "total_cost": float(row["total_cost"] or 0)
                        }
                        for row in results
                    ]
        except Exception:
            return []


task_cost_db = TaskCostDatabase()
