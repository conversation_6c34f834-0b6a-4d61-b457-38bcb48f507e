import pymysql
import json
from datetime import datetime
from typing import List, Optional
from contextlib import contextmanager

from core.logging_config import get_logger
from config.settings import settings
from database.ai import VideoData, VideoAnalysisResult

logger = get_logger(__name__)


class RealEstateDatabase:

    def __init__(self):
        self.connection_config = {
            'host': settings.MYSQL_HOST,
            'port': settings.MYSQL_PORT,
            'user': settings.MYSQL_USER,
            'password': settings.MYSQL_PASSWORD,
            'database': settings.MYSQL_DATABASE,
            'charset': settings.MYSQL_CHARSET,
            'autocommit': True
        }
        self._test_connection()

    def _test_connection(self):
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
        except Exception:
            raise
    
    @contextmanager
    def _get_connection(self):
        connection = None
        try:
            connection = pymysql.connect(**self.connection_config)
            yield connection
        except Exception:
            raise
        finally:
            if connection:
                connection.close()
    
    def get_videos_for_analysis(self, limit: int = 100, offset: int = 0) -> List[VideoData]:
        try:
            with self._get_connection() as conn:
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:

                    sql = """
                        SELECT video_id as aweme_id, video_title_description, author_nickname,
                               publish_time as create_time, publish_time as updated_at
                        FROM douyin_video_data
                        WHERE is_related = '0'
                        ORDER BY video_id DESC
                        LIMIT %s OFFSET %s
                    """
                    cursor.execute(sql, (limit, offset))
                    results = cursor.fetchall()

                    return [VideoData(aweme_id=str(row['aweme_id']), **{k: v for k, v in row.items() if k != 'aweme_id'}) for row in results]
        except Exception:
            return []
    
    def get_video_by_aweme_id(self, aweme_id: str) -> Optional[VideoData]:
        try:
            with self._get_connection() as conn:
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                    sql = """
                        SELECT video_id as aweme_id, video_title_description, author_nickname,
                               publish_time as create_time, publish_time as updated_at
                        FROM douyin_video_data
                        WHERE video_id = %s
                    """
                    cursor.execute(sql, (aweme_id,))
                    result = cursor.fetchone()
                    if result:
                        return VideoData(aweme_id=str(result['aweme_id']), **{k: v for k, v in result.items() if k != 'aweme_id'})
                    return None
        except Exception:
            return None
    
    def save_analysis_result(self, result: VideoAnalysisResult) -> bool:
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    # 更新原表的is_related字段
                    is_related_value = '1' if result.is_real_estate_related else '2'
                    update_original_sql = """
                        UPDATE douyin_video_data SET is_related = %s
                        WHERE video_id = %s
                    """
                    cursor.execute(update_original_sql, (is_related_value, result.aweme_id))

                    # 保存详细分析结果到分析结果表
                    check_sql = "SELECT id FROM video_analysis_results WHERE aweme_id = %s"
                    cursor.execute(check_sql, (result.aweme_id,))
                    existing = cursor.fetchone()

                    if existing:
                        update_sql = """
                            UPDATE video_analysis_results SET
                                is_real_estate_related = %s, confidence_score = %s,
                                analysis_result = %s, keywords_found = %s,
                                analysis_time = %s, updated_at = %s
                            WHERE aweme_id = %s
                        """
                        cursor.execute(update_sql, (
                            result.is_real_estate_related, result.confidence_score,
                            result.analysis_result, result.keywords_found,
                            result.analysis_time, datetime.now(), result.aweme_id
                        ))
                    else:
                        insert_sql = """
                            INSERT INTO video_analysis_results (
                                aweme_id, video_title_description, is_real_estate_related,
                                confidence_score, analysis_result, keywords_found,
                                analysis_time, created_at, updated_at
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(insert_sql, (
                            result.aweme_id, result.video_title_description,
                            result.is_real_estate_related, result.confidence_score,
                            result.analysis_result, result.keywords_found,
                            result.analysis_time, datetime.now(), datetime.now()
                        ))
                    return True
        except Exception:
            return False
    

    def create_analysis_table_if_not_exists(self):
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    create_sql = """
                        CREATE TABLE IF NOT EXISTS video_analysis_results (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            aweme_id VARCHAR(255) UNIQUE NOT NULL,
                            video_title_description TEXT,
                            is_real_estate_related BOOLEAN NOT NULL,
                            confidence_score DECIMAL(3,2) NOT NULL,
                            analysis_result TEXT,
                            keywords_found TEXT,
                            analysis_time DATETIME,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            INDEX idx_aweme_id (aweme_id),
                            INDEX idx_is_real_estate (is_real_estate_related),
                            INDEX idx_created_at (created_at)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    """
                    cursor.execute(create_sql)
        except Exception:
            raise


real_estate_db = RealEstateDatabase()
