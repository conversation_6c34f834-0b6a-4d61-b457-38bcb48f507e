from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class CustomerAnalysisResult(BaseModel):
    """客户分析结果数据库模型 - 仅用于签名分析"""
    id: Optional[int] = None
    signature: Optional[str] = None
    status: Optional[str] = "0"  # "0"=待处理, "1"=非厂房销售, "2"=厂房销售

    class Config:
        from_attributes = True


class DouyinVideoData(BaseModel):
    """抖音视频数据模型"""
    video_id: str
    video_title_description: Optional[str] = None
    author_nickname: Optional[str] = None
    comment_count: Optional[int] = None
    publish_time: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
