import pymysql
from contextlib import contextmanager
from typing import List

from config.settings import settings
from database.douyin import DouyinVideoData


class DouyinVideoDatabase:
    """抖音视频数据库服务"""

    def __init__(self):
        self.connection_config = {
            'host': settings.MYSQL_HOST,
            'port': settings.MYSQL_PORT,
            'user': settings.MYSQL_USER,
            'password': settings.MYSQL_PASSWORD,
            'database': settings.MYSQL_DATABASE,
            'charset': settings.MYSQL_CHARSET,
            'autocommit': True
        }
        self._test_connection()

    def _test_connection(self):
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
        except Exception:
            raise
    
    @contextmanager
    def _get_connection(self):
        connection = None
        try:
            connection = pymysql.connect(**self.connection_config)
            yield connection
        except Exception:
            raise
        finally:
            if connection:
                connection.close()
    
    def get_related_videos(self, limit: int = 100, offset: int = 0) -> List[DouyinVideoData]:
        """获取is_related为1的视频数据"""
        try:
            with self._get_connection() as conn:
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                    sql = """
                        SELECT video_id as aweme_id, video_title_description, author_nickname,
                               comment_count, publish_time as create_time
                        FROM douyin_video_data
                        WHERE is_related = '1' and search_topic='长春厂房'
                        ORDER BY publish_time DESC
                        LIMIT %s OFFSET %s
                    """
                    cursor.execute(sql, (limit, offset))
                    results = cursor.fetchall()

                    videos = []
                    for row in results:
                        # 转换数据格式以匹配DouyinVideoData模型
                        video_data = DouyinVideoData(
                            aweme_id=str(row['aweme_id']),
                            video_title_description=row.get('video_title_description'),
                            author_nickname=row.get('author_nickname'),
                            comment_count=row.get('comment_count'),
                            publish_time=row.get('create_time'),
                            updated_at=row.get('create_time')
                        )
                        videos.append(video_data)
                    
                    return videos
        except Exception:
            return []
    
    def get_related_videos_count(self) -> int:
        """获取is_related为1的视频总数"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    sql = "SELECT COUNT(*) as count FROM douyin_video_data WHERE is_related = '1' and search_topic='长春厂房'"
                    cursor.execute(sql)
                    result = cursor.fetchone()
                    return result[0] if result else 0
        except Exception:
            return 0
    





douyin_video_db = DouyinVideoDatabase()
