from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import time
import os

from core.responses import success_response, error_response
from core.errors import ErrorCodes
from core.logging_config import setup_application_logging, get_logger, log_api_access
from config.settings import ensure_directories, DouyinConstants

ensure_directories()
log_level = os.getenv("LOG_LEVEL", "INFO")
setup_application_logging(log_level)

from endpoints.douyin import login_router, search_router_v3, user_search_router, comment_router, video_data_router
from endpoints.douyin_official import oa_router, oauth_router
from endpoints.ai import topic_analysis_router, customer_analysis_router, real_estate_analysis_router
from endpoints.device import device_router

logger = get_logger(__name__)

app = FastAPI(
    title="ReachRadar API",
    description="抖音登录和数据获取API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = (time.time() - start_time) * 1000
    client_ip = request.client.host if request.client else "unknown"
    log_api_access(
        method=request.method,
        path=str(request.url.path),
        status_code=response.status_code,
        response_time=process_time,
        client_ip=client_ip
    )
    return response

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(login_router)
app.include_router(search_router_v3)
app.include_router(user_search_router)
app.include_router(comment_router)
app.include_router(video_data_router)
app.include_router(oa_router)
app.include_router(oauth_router)
app.include_router(topic_analysis_router)
app.include_router(customer_analysis_router)
app.include_router(real_estate_analysis_router)
app.include_router(device_router)

@app.get("/health")
async def health_check():
    try:
        health_data = {
            "status": "healthy",
            "service": "ReachRadar",
            "network_security": {
                "is_safe": True,
                "current_network": None,
                "message": "网络环境安全",
                "restricted": False
            }
        }
        return success_response(data=health_data, message="服务运行正常")
    except Exception as e:
        logger.error(f"健康检查异常: {e}")
        health_data = {
            "status": "healthy",
            "service": "ReachRadar",
            "network_security": {
                "is_safe": True,
                "current_network": None,
                "message": "网络环境安全",
                "restricted": False
            }
        }
        return success_response(data=health_data, message="服务运行正常")

@app.post("/api/network/verify")
async def verify_user_network(network_info: dict):
    try:
        user_ip = network_info.get("ip_address", "")

        if not user_ip:
            return success_response(data={
                "is_safe": True,
                "restricted": False,
                "message": "未提供IP地址信息"
            })

        is_restricted = DouyinConstants.is_ip_restricted(user_ip)
        detected_network = DouyinConstants.get_network_by_ip(user_ip)

        if is_restricted:
            message = f"检测到受限制网络：{detected_network}"
        else:
            message = "网络环境安全"

        return success_response(data={
            "is_safe": not is_restricted,
            "restricted": is_restricted,
            "message": message
        })

    except Exception as e:
        logger.error(f"网络验证异常: {e}")
        return error_response(code=ErrorCodes.UNKNOWN_ERROR, message="网络验证失败")

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"未处理的异常: {type(exc).__name__}: {str(exc)} | 请求路径: {request.url.path} | 请求方法: {request.method}", exc_info=True)
    return error_response(code=ErrorCodes.UNKNOWN_ERROR, message="服务器内部错误，请稍后重试")

if __name__ == "__main__":
    logger.info("启动ReachRadar API服务...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8004,
        reload=True,
        log_level="warning",
        access_log=False
    )
