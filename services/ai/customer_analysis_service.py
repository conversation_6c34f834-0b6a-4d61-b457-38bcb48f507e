import json
import re
import time
from pathlib import Path
from collections import defaultdict
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from openai import OpenAI

from core.errors import ErrorCodes
from core.responses import success_response, error_response
from core.logging_config import get_logger

from models.ai import (
    CommentCustomerAnalysisRequest,
    CommentCustomerAnalysisResult,
    CustomerIntent
)
from models.douyin import CommentInfo
from config.settings import settings
from services.ai import token_cost_calculator
from database.ai import task_cost_db, TaskCostRecord


class CustomerAnalysisService:
    def __init__(self):
        self.model_name = settings.AI_MODEL_NAME
        self.base_url = settings.AI_BASE_URL
        self.temperature = settings.AI_DEFAULT_TEMPERATURE
        self.max_tokens = settings.AI_DEFAULT_MAX_TOKENS
        self.token_usage_records = []
        self.logger = get_logger(__name__)
        self._load_customer_prompt()

    def _load_customer_prompt(self):
        try:
            prompt_file = Path("docs/douyin/comment.md")
            if prompt_file.exists():
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    self.customer_prompt = f.read()
            else:
                self.customer_prompt = ""
        except Exception:
            self.customer_prompt = ""

    def _get_client(self) -> OpenAI:
        return OpenAI(
            api_key=settings.DASHSCOPE_API_KEY,
            base_url=self.base_url
        )

    def _group_comments_by_user(self, comments: List[CommentInfo]) -> Dict[str, List[CommentInfo]]:
        """按用户分组评论"""
        user_comments = defaultdict(list)
        for comment in comments:
            user_key = f"{comment.user_uid}_{comment.user_nickname}"
            user_comments[user_key].append(comment)
        return dict(user_comments)

    def _build_analysis_prompt(self, user_comments: List[CommentInfo]) -> str:
        """构建分析提示词"""
        if not self.customer_prompt:
            raise ValueError("客户分析提示词未加载，请检查docs/douyin/comment.md文件")

        user_info = user_comments[0]
        comments_text = "\n".join([f"评论{i+1}: {comment.text}" for i, comment in enumerate(user_comments)])
        
        analysis_text = f"""
用户信息：
- 昵称：{user_info.user_nickname}
- 抖音号：{user_info.user_short_id}
- 用户ID：{user_info.user_uid}

该用户的所有评论内容：
{comments_text}

请严格按照以下JSON格式返回分析结果：
{{
    "is_potential_customer": true/false,
    "intent_type": "租赁/购买/待定",
    "confidence": 0.0-1.0,
    "reasoning": "具体的判断理由，必须引用评论中的关键词或短语"
}}
"""

        return f"{self.customer_prompt}\n\n{analysis_text}"

    def _analyze_user_intent(self, user_comments: List[CommentInfo], timeout_seconds: int) -> Dict[str, Any]:
        """分析单个用户的客户意向"""
        try:
            client = self._get_client()
            prompt = self._build_analysis_prompt(user_comments)

            completion = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "你是一名专注于抖音/小红书等平台的厂房园区销售顾问，擅长从评论中识别潜在客户。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                timeout=timeout_seconds
            )

            response_content = completion.choices[0].message.content.strip()

            if hasattr(completion, 'usage') and completion.usage:
                self.token_usage_records.append({
                    "model_name": self.model_name,
                    "input_tokens": completion.usage.prompt_tokens,
                    "output_tokens": completion.usage.completion_tokens
                })
            
            try:
                result_data = json.loads(response_content)
            except json.JSONDecodeError:
                json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
                if json_match:
                    result_data = json.loads(json_match.group())
                else:
                    raise ValueError("无法解析AI响应为JSON格式")

            return result_data

        except Exception:
            return {
                "is_potential_customer": False,
                "intent_type": "待定",
                "confidence": 0.0,
                "reasoning": "分析过程中出现错误"
            }

    def _analyze_user_group_wrapper(self, user_key_and_comments: tuple, timeout_seconds: int = 60) -> tuple:
        """包装函数，用于并发处理单个用户组"""
        user_key, user_comments = user_key_and_comments
        analysis_result = self._analyze_user_intent(user_comments, timeout_seconds)
        return user_key, user_comments, analysis_result

    def analyze_customer(self, request: CommentCustomerAnalysisRequest) -> dict:
        start_time = time.time()
        self.token_usage_records = []

        try:
            comments = request.comments

            if not comments:
                return error_response(
                    code=ErrorCodes.AI_INVALID_INPUT,
                    message="没有找到可分析的评论数据"
                )

            if request.group_by_user:
                user_groups = self._group_comments_by_user(comments)
            else:
                user_groups = {f"{comment.user_uid}_{comment.user_nickname}_{i}": [comment]
                              for i, comment in enumerate(comments)}

            potential_customers = []
            total_users_analyzed = len(user_groups)
            total_comments_analyzed = len(comments)

            max_workers = min(request.max_concurrent_requests, len(user_groups))

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_user = {
                    executor.submit(self._analyze_user_group_wrapper, (user_key, user_comments), request.timeout_seconds): user_key
                    for user_key, user_comments in user_groups.items()
                }

                for future in as_completed(future_to_user):
                    try:
                        _, user_comments, analysis_result = future.result()

                        if (analysis_result.get("is_potential_customer", False) and
                            analysis_result.get("confidence", 0.0) >= request.min_confidence):

                            user_info = user_comments[0]
                            video_ids = list(set([comment.aweme_id for comment in user_comments if comment.aweme_id]))
                            video_links = [f"https://www.douyin.com/video/{video_id}" for video_id in video_ids]

                            comment_times = []
                            ip_labels = []
                            latest_timestamp = 0

                            for comment in user_comments:
                                if comment.create_time:
                                    formatted_time = datetime.fromtimestamp(comment.create_time).strftime('%Y-%m-%d %H:%M:%S')
                                    comment_times.append(formatted_time)
                                    latest_timestamp = max(latest_timestamp, comment.create_time)
                                else:
                                    comment_times.append("未知时间")

                                ip_labels.append(comment.ip_label if comment.ip_label else "未知地址")

                            latest_comment_time = datetime.fromtimestamp(latest_timestamp).strftime('%Y-%m-%d %H:%M:%S') if latest_timestamp > 0 else "未知时间"

                            customer_intent = CustomerIntent(
                                user_nickname=user_info.user_nickname,
                                user_uid=user_info.user_uid,
                                user_short_id=user_info.user_short_id,
                                user_sec_uid=user_info.user_sec_uid,
                                intent_type=analysis_result.get("intent_type", "待定"),
                                confidence=float(analysis_result.get("confidence", 0.0)),
                                reasoning=analysis_result.get("reasoning", ""),
                                comments_analyzed=[comment.text for comment in user_comments],
                                comment_count=len(user_comments),
                                video_ids=video_ids,
                                video_links=video_links,
                                comment_times=comment_times,
                                ip_labels=ip_labels,
                                latest_comment_time=latest_comment_time
                            )
                            potential_customers.append(customer_intent)

                    except Exception as e:
                        self.logger.error(f"处理用户组分析结果失败: {str(e)}")

            potential_customers.sort(key=lambda x: x.confidence, reverse=True)

            processing_time = time.time() - start_time

            token_cost_info = None
            if self.token_usage_records:
                token_cost_info = token_cost_calculator.calculate_batch_cost(self.token_usage_records)

            analysis_summary = f"分析了{total_users_analyzed}个用户的{total_comments_analyzed}条评论，识别出{len(potential_customers)}个潜在客户"
            if token_cost_info:
                analysis_summary += f"，消耗{token_cost_info['total_tokens']}个token，成本{token_cost_info['total_cost']:.6f}元"

            result = CommentCustomerAnalysisResult(
                potential_customers=potential_customers,
                total_users_analyzed=total_users_analyzed,
                total_comments_analyzed=total_comments_analyzed,
                processing_time=processing_time,
                model_used=self.model_name,
                analysis_summary=analysis_summary
            )

            result_data = result.model_dump()
            if token_cost_info:
                result_data["token_cost_info"] = token_cost_info

                total_input_tokens = sum(r["input_tokens"] for r in self.token_usage_records)
                total_output_tokens = sum(r["output_tokens"] for r in self.token_usage_records)

                cost_record = TaskCostRecord(
                    task_type="customer_analysis",
                    model_name=self.model_name,
                    input_tokens=total_input_tokens,
                    output_tokens=total_output_tokens,
                    total_tokens=token_cost_info["total_tokens"],
                    input_cost=token_cost_info["total_cost"] * 0.2,
                    output_cost=token_cost_info["total_cost"] * 0.8,
                    total_cost=token_cost_info["total_cost"],
                    task_data_count=total_comments_analyzed,
                    processing_time=processing_time
                )
                task_cost_db.save_task_cost(cost_record)

            return success_response(
                data=result_data,
                message=f"客户分析完成，识别出{len(potential_customers)}个潜在客户"
            )

        except Exception as e:
            print("客户分析错误：",str(e))
            return error_response(
                code=ErrorCodes.AI_SERVICE_ERROR,
                message="客户分析服务暂时不可用，请稍后重试"
            )


customer_analysis_service = CustomerAnalysisService()
