from fastapi import APIRouter

from core.responses import error_response
from core.errors import ErrorCodes
from core.logging_config import get_logger, log_error_with_context
from models.douyin import SearchRequest
from services.douyin import douyin_search_service_v3

router = APIRouter(
    prefix="/api/douyin",
    tags=["抖音搜索V3"]
)
logger = get_logger(__name__)


@router.post("/search/v3")
def search_douyin_v3(request: SearchRequest):
    try:
        result = douyin_search_service_v3.search_videos(request)
        return result

    except Exception as e:
        log_error_with_context(
            logger,
            f"搜索失败: {str(e)}",
            {
                "keyword": request.keyword,
                "pages": request.pages,
                "error_type": type(e).__name__
            }
        )
        return error_response(
            ErrorCodes.INTERNAL_SERVER_ERROR,
            "搜索服务暂时不可用，请稍后重试"
        )



