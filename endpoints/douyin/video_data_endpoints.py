from typing import Dict, Any
from fastapi import APIRouter, Query

from core.responses import success_response, error_response
from core.errors import ErrorCodes
from core.logging_config import get_logger, log_error_with_context
from database.douyin.video_database import douyin_video_db

logger = get_logger(__name__)

router = APIRouter(
    prefix="/api/douyin/video-data",
    tags=["抖音视频数据"],
    responses={
        ErrorCodes.NOT_FOUND_HTTP: {"description": "未找到"},
        ErrorCodes.INTERNAL_SERVER_ERROR: {"description": "服务器内部错误"}
    }
)


@router.get("/related", response_model=Dict[str, Any])
async def get_related_videos(
    limit: int = Query(default=100, ge=1, le=1000, description="获取数量限制"),
    offset: int = Query(default=0, ge=0, description="偏移量")
) -> Dict[str, Any]:
    """获取is_related为1的相关视频数据"""
    try:
        videos = douyin_video_db.get_related_videos(limit=limit, offset=offset)
        total_count = douyin_video_db.get_related_videos_count()
        
        return success_response(
            data={
                "videos": [video.model_dump() for video in videos],
                "total_count": total_count,
                "current_count": len(videos),
                "has_more": offset + len(videos) < total_count
            },
            message=f"成功获取 {len(videos)} 个相关视频数据"
        )

    except Exception as e:
        log_error_with_context(
            logger,
            "获取相关视频数据失败",
            error=e,
            context={"limit": limit, "offset": offset}
        )
        return error_response(
            code=ErrorCodes.DATABASE_ERROR,
            message="获取视频数据失败，请稍后重试"
        )



