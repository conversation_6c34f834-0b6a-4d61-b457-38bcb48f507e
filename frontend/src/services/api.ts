import axios from 'axios';
import type { AxiosResponse } from 'axios';
import type {
  ApiResponse, LoginSession, LoginStartRequest, CookieResponse,
  CookieValidationRequest, CookieValidationResponse, SearchRequest,
  SearchResponse, CommentRequest, CommentResult, CommentCustomerAnalysisRequest,
  CommentCustomerAnalysisResult, DeviceVerificationRequest, DeviceVerificationResponse,
  HealthCheckResponse, OAAuthRequest, OAAuthUrlResponse,
  OATokenInfo, OAUserInfo, OATokenValidateRequest, OATokenValidateResponse,
  RealEstateAnalysisRequest, RealEstateAnalysisResponse
} from '../types';

const api = axios.create({
  baseURL: '',
  timeout: 300000,
  headers: {
    'Content-Type': 'application/json',
  },
});

api.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    if (response.data && response.data.code !== 200) {
      const message = response.data.message || '操作失败';
      const error = new Error(message) as any;
      error.response = response;
      throw error;
    }

    return response;
  },
  (error) => {
    if (error.response) {
      const message = error.response.data?.message || '服务器错误';
      const newError = new Error(message) as any;
      newError.response = error.response;
      throw newError;
    } else if (error.request) {
      throw new Error('网络连接失败，请检查后端服务是否启动');
    } else {
      throw new Error(error.message || '未知错误');
    }
  }
);

export const douyinApi = {
  startLogin: async (request: LoginStartRequest): Promise<LoginSession> => {
    const response = await api.post<ApiResponse<LoginSession>>('/api/douyin/login/start', request);
    return response.data.data;
  },

  getLoginStatus: async (sessionId: string): Promise<LoginSession> => {
    const response = await api.get<ApiResponse<LoginSession>>(`/api/douyin/login/status/${sessionId}`);
    return response.data.data;
  },

  confirmLogin: async (sessionId: string): Promise<LoginSession> => {
    const response = await api.post<ApiResponse<LoginSession>>(`/api/douyin/login/confirm/${sessionId}`);
    return response.data.data;
  },

  getCookies: async (sessionId: string): Promise<CookieResponse> => {
    const response = await api.get<ApiResponse<CookieResponse>>(`/api/douyin/login/cookies/${sessionId}`);
    return response.data.data;
  },

  cancelLogin: async (sessionId: string): Promise<void> => {
    await api.delete(`/api/douyin/login/cancel/${sessionId}`);
  },

  validateCookies: async (request: CookieValidationRequest): Promise<CookieValidationResponse> => {
    const response = await api.post<ApiResponse<CookieValidationResponse>>('/api/douyin/login/validate', request);
    return response.data.data;
  },

  searchVideos: async (request: SearchRequest): Promise<SearchResponse> => {
    const backendTimeout = request.timeout_seconds || 600;
    const frontendTimeout = (backendTimeout + 60) * 1000; // 后端超时+1分钟缓冲，转换为毫秒
    const response = await api.post<ApiResponse<SearchResponse>>('/api/douyin/search/v3', request, {
      timeout: frontendTimeout
    });
    return response.data.data;
  },

  getComments: async (request: CommentRequest): Promise<CommentResult> => {
    const response = await api.post<ApiResponse<CommentResult>>('/api/douyin/comment/get', request, {
      timeout: 600000
    });
    return response.data.data;
  },

  getRelatedVideos: async (limit: number = 100, offset: number = 0): Promise<any> => {
    const response = await api.get<ApiResponse<any>>(`/api/douyin/video-data/related?limit=${limit}&offset=${offset}`);
    return response.data.data;
  },


};

export const aiApi = {
  analyzeCustomer: async (request: CommentCustomerAnalysisRequest): Promise<CommentCustomerAnalysisResult> => {
    const response = await api.post<ApiResponse<CommentCustomerAnalysisResult>>('/api/ai/customer/analyze', request, {
      timeout: 2500000  // 40分钟超时+缓冲
    });
    return response.data.data;
  },

  analyzeRealEstate: async (request: RealEstateAnalysisRequest): Promise<RealEstateAnalysisResponse> => {
    const response = await api.post<ApiResponse<RealEstateAnalysisResponse>>('/api/ai/real-estate/analyze', request, {
      timeout: 600000
    });
    return response.data.data;
  },
};

export const deviceApi = {
  verify: async (request: DeviceVerificationRequest): Promise<DeviceVerificationResponse> => {
    const response = await api.post<ApiResponse<DeviceVerificationResponse>>('/api/device/verify', request);
    return response.data.data;
  },

  getInfo: async (): Promise<{ verification_enabled: boolean; status: string }> => {
    const response = await api.get<ApiResponse<{ verification_enabled: boolean; status: string }>>('/api/device/info');
    return response.data.data;
  },
};

export const healthApi = {
  check: async (): Promise<HealthCheckResponse> => {
    try {
      const response = await api.get<ApiResponse<HealthCheckResponse>>('/health');
      return response.data.data;
    } catch (error) {
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        if (axiosError.response?.data?.data) {
          return axiosError.response.data.data;
        }
      }
      throw error;
    }
  },
};

export const douyinOAApi = {
  // 生成授权URL
  generateAuthUrl: async (request: OAAuthRequest): Promise<OAAuthUrlResponse> => {
    const response = await api.post<ApiResponse<OAAuthUrlResponse>>('/api/douyin-official/oa/auth', request);
    return response.data.data;
  },

  // 获取用户信息
  getUserInfo: async (accessToken: string, openId: string): Promise<OAUserInfo> => {
    const response = await api.get<ApiResponse<OAUserInfo>>(`/api/douyin-official/oa/user/info?access_token=${accessToken}&open_id=${openId}`);
    return response.data.data;
  },

  // 验证token
  validateToken: async (request: OATokenValidateRequest): Promise<OATokenValidateResponse> => {
    const response = await api.post<ApiResponse<OATokenValidateResponse>>('/api/douyin-official/oa/token/validate', request);
    return response.data.data;
  },

  // 刷新token
  refreshToken: async (refreshToken: string, clientKey: string): Promise<OATokenInfo> => {
    const response = await api.post<ApiResponse<OATokenInfo>>('/api/douyin-official/oa/token/refresh', {
      refresh_token: refreshToken,
      client_key: clientKey
    });
    return response.data.data;
  },
};

export default api;
