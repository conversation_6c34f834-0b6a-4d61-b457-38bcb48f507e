import React, { useState, useEffect, useCallback } from 'react';
import { Trash2, Play, Pause, Download, AlertTriangle } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import Card from './Card';
import Button from './Button';
import { douyinApi } from '../services/api';
import { formatTime } from '../utils/formatters';
import { useSimpleNetworkCheck } from '../hooks/useNetworkSecurity';
import TopicConfigPanel from './TopicConfigPanel';
import type { SearchResponse, VideoInfo, TopicConfig, SearchConfig } from '../types';

interface TopicSearchTask {
  id: string;
  keyword: string;
  status: 'pending' | 'searching' | 'completed' | 'failed';
  progress: number;
  results?: SearchResponse;
  error?: string;
  startTime?: string;
  endTime?: string;
}

interface BatchSearchState {
  tasks: TopicSearchTask[];
  currentTaskIndex: number;
  isRunning: boolean;
  totalProgress: number;
}

interface TopicBatchSearchProps {
  onSearchComplete?: (results: any[]) => void;
}

const TopicBatchSearchComponent: React.FC<TopicBatchSearchProps> = ({
  onSearchComplete
}) => {
  const { theme } = useTheme();
  const { checkNetworkSecurity } = useSimpleNetworkCheck();

  const generateTopicId = () => {
    return `topic_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  };

  const createDefaultTopics = useCallback((): TopicConfig[] => {
    const defaultKeywords = [
      '北京厂房',
      '北京厂房出租',
      '北京厂房出售',
      '北京工业园区',
      '北京产业园',
      '北京仓储物流'
    ];

    return defaultKeywords.map((keyword) => ({
      id: generateTopicId(),
      keyword
    }));
  }, []);

  const [topics, setTopics] = useState<TopicConfig[]>([]);
  const [searchConfig, setSearchConfig] = useState<SearchConfig>({
    totalCount: 500
  });
  const [batchState, setBatchState] = useState<BatchSearchState>({
    tasks: [],
    currentTaskIndex: -1,
    isRunning: false,
    totalProgress: 0
  });
  const [callbackExecuted, setCallbackExecuted] = useState(false);

  useEffect(() => {
    if (topics.length === 0) {
      setTopics(createDefaultTopics());
    }
  }, [topics.length, createDefaultTopics]);

  useEffect(() => {
    if (!batchState.isRunning && batchState.tasks.length > 0 && onSearchComplete && !callbackExecuted) {
      const completedTasks = batchState.tasks.filter(task => task.status === 'completed');

      if (completedTasks.length === batchState.tasks.length) {
        const allVideos: any[] = [];

        completedTasks.forEach(task => {
          if (task.results) {
            const videos = task.results.result?.videos || [];
            allVideos.push(...videos);
          }
        });

        if (allVideos.length > 0) {
          setCallbackExecuted(true);
          onSearchComplete?.(allVideos);
        }
      }
    }
  }, [batchState.isRunning, batchState.tasks, onSearchComplete, callbackExecuted]);



  const generateTaskId = () => {
    return `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  };

  const executeSearchTask = async (task: TopicSearchTask): Promise<TopicSearchTask> => {
    try {
      const isNetworkSafe = await checkNetworkSecurity();
      if (!isNetworkSafe) {
        throw new Error('当前网络受限制，请切换网络后重试');
      }

      const startTime = new Date().toISOString();

      const pages = Math.ceil(searchConfig.totalCount / 10);
      const timeoutSeconds = Math.min(7200, Math.max(600, Math.ceil(searchConfig.totalCount / 1000 * 3600)));
      const result = await douyinApi.searchVideos({
        keyword: task.keyword,
        pages: pages - 1,
        per_page: 10,
        timeout_seconds: timeoutSeconds
      });

      const endTime = new Date().toISOString();

      return {
        ...task,
        status: 'completed',
        progress: 100,
        results: result,
        startTime,
        endTime
      };

    } catch (error) {
      return {
        ...task,
        status: 'failed',
        progress: 0,
        error: error instanceof Error ? error.message : '搜索失败',
        endTime: new Date().toISOString()
      };
    }
  };

  const handleStartBatchSearch = async () => {
    if (topics.length === 0) {
      return;
    }

    const tasks: TopicSearchTask[] = topics.map((topicConfig) => {
      return {
        id: generateTaskId(),
        keyword: topicConfig.keyword,
        status: 'pending',
        progress: 0
      };
    });

    setCallbackExecuted(false);
    setBatchState({
      tasks,
      isRunning: true,
      currentTaskIndex: 0,
      totalProgress: 0
    });

    for (let i = 0; i < tasks.length; i++) {
      setBatchState(prev => ({
        ...prev,
        currentTaskIndex: i,
        tasks: prev.tasks.map((task, index) => 
          index === i ? { ...task, status: 'searching' } : task
        )
      }));

      const updatedTask = await executeSearchTask(tasks[i]);
      
      setBatchState(prev => ({
        ...prev,
        tasks: prev.tasks.map((task, index) => 
          index === i ? updatedTask : task
        ),
        totalProgress: Math.round(((i + 1) / tasks.length) * 100)
      }));

      if (i < tasks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    setBatchState(prev => ({
      ...prev,
      isRunning: false,
      currentTaskIndex: -1
    }));
  };

  const handleStopBatchSearch = () => {
    setBatchState(prev => ({
      ...prev,
      isRunning: false,
      currentTaskIndex: -1
    }));
  };

  const handleClearResults = () => {
    setBatchState({
      tasks: [],
      currentTaskIndex: -1,
      isRunning: false,
      totalProgress: 0
    });
  };

  const getSearchStats = () => {
    const completed = batchState.tasks.filter(task => task.status === 'completed').length;
    const failed = batchState.tasks.filter(task => task.status === 'failed').length;
    const totalVideos = batchState.tasks
      .filter(task => task.results)
      .reduce((sum, task) => sum + (task.results?.result?.videos?.length || 0), 0);

    return { completed, failed, totalVideos };
  };



  const handleExportResults = () => {
    const completedTasks = batchState.tasks.filter(task => task.status === 'completed' && task.results);

    if (completedTasks.length === 0) {
      return;
    }

    const allVideoData: Array<{
      task: TopicSearchTask;
      video: VideoInfo;
    }> = [];

    completedTasks.forEach(task => {
      const videos = task.results?.result?.videos || [];

      videos.forEach(video => {
        allVideoData.push({
          task,
          video
        });
      });
    });

    allVideoData.sort((a, b) => (b.video.create_time || 0) - (a.video.create_time || 0));

    const csvData: string[][] = [];

    csvData.push([
      '搜索话题',
      '视频ID',
      '视频标题/描述',
      '作者昵称',
      '作者抖音号',
      '点赞数',
      '评论数',
      '分享数',
      '播放数',
      '发布时间',
      '视频链接'
    ]);

    allVideoData.forEach(({ task, video }) => {
      csvData.push([
        task.keyword,
        video.aweme_id,
        `"${(video.desc || '').replace(/"/g, '""')}"`,
        `"${(video.author_nickname || '').replace(/"/g, '""')}"`,
        video.author_unique_id || '-',
        (video.statistics?.digg_count || 0).toString(),
        (video.statistics?.comment_count || 0).toString(),
        (video.statistics?.share_count || 0).toString(),
        (video.statistics?.play_count || 0).toString(),
        formatTime(video.create_time, { fallback: '-' }),
        video.douyin_link || `https://www.douyin.com/video/${video.aweme_id}`
      ]);
    });

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `liandong_search_results_${new Date().getTime()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const stats = getSearchStats();

  return (
    <div className="space-y-6">
      <TopicConfigPanel
        topics={topics}
        onTopicsChange={setTopics}
        searchConfig={searchConfig}
        onSearchConfigChange={setSearchConfig}
      />

      <Card variant="glass">
        <div className="flex items-center justify-between mb-4">
          <h3 className={`text-lg font-semibold transition-colors duration-300 ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            批量搜索控制
          </h3>
          <div className="flex items-center space-x-3">
            {!batchState.isRunning ? (
              <Button
                onClick={handleStartBatchSearch}
                disabled={topics.length === 0}
                className="bg-green-500 hover:bg-green-600 flex items-center space-x-2"
              >
                <Play className="h-4 w-4" />
                <span>开始搜索</span>
              </Button>
            ) : (
              <Button
                onClick={handleStopBatchSearch}
                className="bg-red-500 hover:bg-red-600 flex items-center space-x-2"
              >
                <Pause className="h-4 w-4" />
                <span>停止搜索</span>
              </Button>
            )}
            
            {batchState.tasks.length > 0 && (
              <Button
                onClick={handleClearResults}
                disabled={batchState.isRunning}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <Trash2 className="h-4 w-4" />
                <span>清空结果</span>
              </Button>
            )}
          </div>
        </div>

        <div className={`p-4 rounded-lg transition-colors duration-300 ${
          theme === 'dark' ? 'bg-dark-800/50' : 'bg-gray-50'
        }`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className={`font-medium transition-colors duration-300 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                搜索配置：
              </span>
              <span className={`ml-2 transition-colors duration-300 ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                每个话题{searchConfig.totalCount}条
              </span>
            </div>
            <div>
              <span className={`font-medium transition-colors duration-300 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                使用策略：
              </span>
              <span className={`ml-2 transition-colors duration-300 ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                V3接口无需Cookie验证
              </span>
            </div>
            <div>
              <span className={`font-medium transition-colors duration-300 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                预计获取：
              </span>
              <span className={`ml-2 transition-colors duration-300 ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                {topics.length * searchConfig.totalCount} 条视频数据
              </span>
            </div>
          </div>
        </div>
      </Card>

      {batchState.tasks.length > 0 && (
        <Card variant="glass">
          <div className="flex items-center justify-between mb-4">
            <h3 className={`text-lg font-semibold transition-colors duration-300 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              搜索进度
            </h3>
            <div className="flex items-center space-x-4 text-sm">
              <span className={`transition-colors duration-300 ${
                theme === 'dark' ? 'text-green-400' : 'text-green-600'
              }`}>
                已完成: {stats.completed}
              </span>
              <span className={`transition-colors duration-300 ${
                theme === 'dark' ? 'text-red-400' : 'text-red-600'
              }`}>
                失败: {stats.failed}
              </span>
              <span className={`transition-colors duration-300 ${
                theme === 'dark' ? 'text-blue-400' : 'text-blue-600'
              }`}>
                获取视频: {stats.totalVideos}
              </span>
            </div>
          </div>

          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className={`text-sm font-medium transition-colors duration-300 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                总体进度
              </span>
              <span className={`text-sm transition-colors duration-300 ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                {batchState.totalProgress}%
              </span>
            </div>
            <div className={`w-full bg-gray-200 rounded-full h-2 transition-colors duration-300 ${
              theme === 'dark' ? 'bg-dark-700' : 'bg-gray-200'
            }`}>
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${batchState.totalProgress}%` }}
              ></div>
            </div>
          </div>

          <div className="space-y-3">
            {batchState.tasks.map((task, index) => (
              <div
                key={task.id}
                className={`p-4 rounded-lg border transition-all duration-300 ${
                  theme === 'dark'
                    ? 'bg-dark-800/50 border-dark-600'
                    : 'bg-gray-50 border-gray-200'
                } ${
                  batchState.currentTaskIndex === index
                    ? theme === 'dark'
                      ? 'ring-2 ring-blue-500/50'
                      : 'ring-2 ring-blue-500/50'
                    : ''
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      task.status === 'completed' ? 'bg-green-500' :
                      task.status === 'failed' ? 'bg-red-500' :
                      task.status === 'searching' ? 'bg-blue-500 animate-pulse' :
                      theme === 'dark' ? 'bg-dark-600' : 'bg-gray-300'
                    }`}></div>
                    <div>
                      <span className={`font-medium transition-colors duration-300 ${
                        theme === 'dark' ? 'text-white' : 'text-gray-900'
                      }`}>
                        {task.keyword}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    {task.status === 'completed' && task.results && (
                      <span className={`text-sm transition-colors duration-300 ${
                        theme === 'dark' ? 'text-green-400' : 'text-green-600'
                      }`}>
                        获取 {task.results.result?.videos?.length || 0} 条视频
                      </span>
                    )}

                    {task.status === 'failed' && (
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        <span className={`text-sm transition-colors duration-300 ${
                          theme === 'dark' ? 'text-red-400' : 'text-red-600'
                        }`}>
                          {task.error}
                        </span>
                      </div>
                    )}

                    <span className={`text-sm transition-colors duration-300 ${
                      theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                    }`}>
                      {task.status === 'pending' ? '等待中' :
                       task.status === 'searching' ? '搜索中...' :
                       task.status === 'completed' ? '已完成' :
                       task.status === 'failed' ? '失败' : ''}
                    </span>
                  </div>
                </div>

                {task.status === 'searching' && (
                  <div className="mt-3">
                    <div className={`w-full bg-gray-200 rounded-full h-1 transition-colors duration-300 ${
                      theme === 'dark' ? 'bg-dark-700' : 'bg-gray-200'
                    }`}>
                      <div className="bg-blue-500 h-1 rounded-full animate-pulse w-1/3"></div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </Card>
      )}

      {batchState.tasks.some(task => task.status === 'completed') && (
        <Card variant="glass">
          <div className="flex items-center justify-between mb-4">
            <h3 className={`text-lg font-semibold transition-colors duration-300 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              搜索结果汇总
            </h3>
            <Button
              onClick={handleExportResults}
              className="bg-blue-500 hover:bg-blue-600 flex items-center space-x-2"
              disabled={stats.totalVideos === 0}
            >
              <Download className="h-4 w-4" />
              <span>导出结果</span>
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {batchState.tasks
              .filter(task => task.status === 'completed' && task.results)
              .map((task) => (
                <div
                  key={task.id}
                  className={`p-4 rounded-lg border transition-all duration-300 ${
                    theme === 'dark'
                      ? 'bg-dark-800/50 border-dark-600'
                      : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className={`font-medium transition-colors duration-300 ${
                      theme === 'dark' ? 'text-white' : 'text-gray-900'
                    }`}>
                      {task.keyword}
                    </h4>
                    <span className={`text-sm transition-colors duration-300 ${
                      theme === 'dark' ? 'text-green-400' : 'text-green-600'
                    }`}>
                      {task.results?.result?.videos?.length || 0} 条
                    </span>
                  </div>

                  <div className={`text-sm space-y-1 transition-colors duration-300 ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    <div>搜索时间: {task.startTime ? new Date(task.startTime).toLocaleString() : '-'}</div>
                    <div>完成时间: {task.endTime ? new Date(task.endTime).toLocaleString() : '-'}</div>
                    {task.results?.result?.total_count && (
                      <div>总数量: {task.results.result.total_count}</div>
                    )}
                  </div>
                </div>
              ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default TopicBatchSearchComponent;
