import React, { useState } from 'react';
import { Settings, Loader2, Download } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import Card from './Card';
import Button from './Button';
import { aiApi } from '../services/api';
import { useSimpleNetworkCheck } from '../hooks/useNetworkSecurity';
import type { CommentResult, CommentCustomerAnalysisResult } from '../types';

const CustomerAnalysisStatus = {
  IDLE: 'idle',
  ANALYZING: 'analyzing',
  SUCCESS: 'success',
  ERROR: 'error'
} as const;

type CustomerAnalysisStatusType = typeof CustomerAnalysisStatus[keyof typeof CustomerAnalysisStatus];

interface CustomerAnalysisProps {
  commentResult: CommentResult | null;
  showCustomerDetails?: boolean; // 是否显示客户详情，默认为true
}

const CustomerAnalysisComponent: React.FC<CustomerAnalysisProps> = ({
  commentResult,
  showCustomerDetails = true
}) => {
  const { theme } = useTheme();
  const { checkNetworkSecurity } = useSimpleNetworkCheck();
  const [customerAnalysisStatus, setCustomerAnalysisStatus] = useState<CustomerAnalysisStatusType>(CustomerAnalysisStatus.IDLE);
  const [customerAnalysisError, setCustomerAnalysisError] = useState<string | null>(null);
  const [customerAnalysisResult, setCustomerAnalysisResult] = useState<CommentCustomerAnalysisResult | null>(null);

  const handleAnalyzeCustomer = async () => {
    if (!commentResult || commentResult.comments.length === 0) {
      setCustomerAnalysisError('没有可分析的评论数据');
      return;
    }

    const isNetworkSafe = await checkNetworkSecurity();
    if (!isNetworkSafe) {
      setCustomerAnalysisError('当前网络受限制，请切换网络后重试');
      return;
    }

    try {
      setCustomerAnalysisStatus(CustomerAnalysisStatus.ANALYZING);
      setCustomerAnalysisError(null);
      setCustomerAnalysisResult(null);

      const requestData = {
        comments: commentResult.comments,
        group_by_user: true,
        min_confidence: 0.6,
        timeout_seconds: 1200,
        max_concurrent_requests: 15
      };

      const result = await aiApi.analyzeCustomer(requestData);

      setCustomerAnalysisResult(result);
      setCustomerAnalysisStatus(CustomerAnalysisStatus.SUCCESS);

    } catch (error) {
      setCustomerAnalysisStatus(CustomerAnalysisStatus.ERROR);
      let errorMessage = '客户分析失败，请稍后重试';

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: { message?: string } } };
        if (axiosError.response?.data?.message) {
          errorMessage = axiosError.response.data.message;
        }
      }

      setCustomerAnalysisError(errorMessage);
    }
  };

  const downloadAnalysisResult = () => {
    if (!customerAnalysisResult) return;

    const csvContent = [
      ['用户昵称', '抖音号', 'SecUID', '用户ID', '意向类型', '置信度', '分析原因', '评论内容', '评论数量', '评论时间', 'IP地址', '最新评论时间', '相关视频链接'].join(','),
      ...customerAnalysisResult.potential_customers.map((user: any) => [
        `"${user.user_nickname}"`,
        user.user_short_id,
        user.user_sec_uid,
        user.user_uid,
        user.intent_type,
        user.confidence.toString(),
        `"${user.reasoning.replace(/"/g, '""')}"`,
        `"${user.comments_analyzed.join('; ').replace(/"/g, '""')}"`,
        user.comment_count.toString(),
        `"${(user.comment_times || []).join('; ').replace(/"/g, '""')}"`,
        `"${(user.ip_labels || []).join('; ').replace(/"/g, '""')}"`,
        user.latest_comment_time || '未知时间',
        `"${(user.video_links || []).join('; ').replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `customer_analysis_${new Date().getTime()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };



  return (
    <Card variant="glass" padding="lg" className="mb-8">
      <h3 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${
        theme === 'dark' ? 'text-white' : 'text-gray-900'
      }`}>
        客户分析
      </h3>

      <div className="space-y-4">
        <div className="flex space-x-3">
          <Button
            onClick={handleAnalyzeCustomer}
            disabled={customerAnalysisStatus === CustomerAnalysisStatus.ANALYZING || !commentResult?.comments.length}
            className="bg-purple-500 hover:bg-purple-600 flex items-center space-x-2"
          >
            {customerAnalysisStatus === CustomerAnalysisStatus.ANALYZING ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Settings className="h-4 w-4" />
            )}
            <span>
              {customerAnalysisStatus === CustomerAnalysisStatus.ANALYZING ? '分析中...' : '开始分析'}
            </span>
          </Button>

          {customerAnalysisResult && (
            <Button
              onClick={downloadAnalysisResult}
              className="bg-blue-500 hover:bg-blue-600 flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>下载结果</span>
            </Button>
          )}
        </div>

        {customerAnalysisError && (
          <div className={`p-3 rounded-lg ${
            theme === 'dark' ? 'bg-red-900/20 text-red-400' : 'bg-red-50 text-red-600'
          }`}>
            {customerAnalysisError}
          </div>
        )}

        {customerAnalysisResult && (
          <div className="space-y-4">
            <div className={`p-4 rounded-lg ${
              theme === 'dark' ? 'bg-dark-800' : 'bg-gray-50'
            }`}>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className={`text-2xl font-bold ${
                    theme === 'dark' ? 'text-blue-400' : 'text-blue-600'
                  }`}>
                    {customerAnalysisResult.total_users_analyzed}
                  </div>
                  <div className={`text-sm ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    分析用户
                  </div>
                </div>
                <div>
                  <div className={`text-2xl font-bold ${
                    theme === 'dark' ? 'text-green-400' : 'text-green-600'
                  }`}>
                    {customerAnalysisResult.potential_customers.length}
                  </div>
                  <div className={`text-sm ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    潜在客户
                  </div>
                </div>
                <div>
                  <div className={`text-2xl font-bold ${
                    theme === 'dark' ? 'text-purple-400' : 'text-purple-600'
                  }`}>
                    {customerAnalysisResult.processing_time}s
                  </div>
                  <div className={`text-sm ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    分析时间
                  </div>
                </div>
                <div>
                  <div className={`text-2xl font-bold ${
                    theme === 'dark' ? 'text-orange-400' : 'text-orange-600'
                  }`}>
                    {customerAnalysisResult.total_comments_analyzed}
                  </div>
                  <div className={`text-sm ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    分析评论
                  </div>
                </div>
              </div>
            </div>

            {showCustomerDetails && (
              <div className={`max-h-96 overflow-y-auto rounded-lg border ${
                theme === 'dark' ? 'border-dark-600' : 'border-gray-200'
              }`}>
              <div className="space-y-2 p-4">
                {customerAnalysisResult.potential_customers.map((user: any, index: number) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg ${
                      theme === 'dark' ? 'bg-dark-700' : 'bg-white'
                    } border ${
                      theme === 'dark' ? 'border-dark-600' : 'border-gray-200'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className={`font-medium ${
                          theme === 'dark' ? 'text-white' : 'text-gray-900'
                        }`}>
                          {user.user_nickname}
                        </span>
                        <span className={`text-sm ${
                          theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                        }`}>
                          @{user.user_short_id}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          user.intent_type === '租赁'
                            ? (theme === 'dark' ? 'bg-green-900/30 text-green-400' : 'bg-green-100 text-green-600')
                            : user.intent_type === '购买'
                            ? (theme === 'dark' ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-100 text-blue-600')
                            : (theme === 'dark' ? 'bg-gray-900/30 text-gray-400' : 'bg-gray-100 text-gray-600')
                        }`}>
                          {user.intent_type}
                        </span>
                        <span className={`text-xs ${
                          user.confidence >= 0.8
                            ? (theme === 'dark' ? 'text-green-400' : 'text-green-600')
                            : user.confidence >= 0.6
                            ? (theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600')
                            : (theme === 'dark' ? 'text-red-400' : 'text-red-600')
                        }`}>
                          {Math.round(user.confidence * 100)}%
                        </span>
                      </div>
                    </div>
                    <p className={`text-sm mb-2 ${
                      theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                    }`}>
                      {user.reasoning}
                    </p>
                    <div className="mb-2">
                      <div className={`text-xs font-medium mb-1 ${
                        theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                      }`}>
                        相关评论：
                      </div>
                      {user.comments_analyzed.map((comment: string, commentIndex: number) => (
                        <div
                          key={commentIndex}
                          className={`text-sm p-2 mb-1 rounded border-l-2 ${
                            theme === 'dark'
                              ? 'bg-dark-700/50 border-l-purple-400 text-dark-100'
                              : 'bg-gray-50 border-l-purple-500 text-gray-800'
                          }`}
                        >
                          "{comment}"
                        </div>
                      ))}
                    </div>
                    <div className="flex items-center space-x-4 text-xs">
                      <div className={`${
                        theme === 'dark' ? 'text-dark-400' : 'text-gray-500'
                      }`}>
                        评论数量: {user.comment_count}
                      </div>
                      <div className={`font-mono ${
                        theme === 'dark' ? 'text-dark-400' : 'text-gray-500'
                      }`}>
                        ID: {user.user_uid}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              </div>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};

export default CustomerAnalysisComponent;
