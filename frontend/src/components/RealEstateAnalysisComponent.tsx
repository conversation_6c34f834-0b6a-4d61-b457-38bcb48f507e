import React, { useState } from 'react';
import { Building2, Play, CheckCircle, AlertTriangle, Clock, TrendingUp } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import Card from './Card';
import Button from './Button';
import { aiApi } from '../services/api';
import { useSimpleNetworkCheck } from '../hooks/useNetworkSecurity';
import type { RealEstateAnalysisResponse } from '../types';

const AnalysisStatus = {
  IDLE: 'idle',
  ANALYZING: 'analyzing',
  COMPLETED: 'completed',
  ERROR: 'error'
} as const;

type AnalysisStatusType = typeof AnalysisStatus[keyof typeof AnalysisStatus];

const RealEstateAnalysisComponent: React.FC = () => {
  const { theme } = useTheme();
  const { checkNetworkSecurity } = useSimpleNetworkCheck();
  
  const [analysisStatus, setAnalysisStatus] = useState<AnalysisStatusType>(AnalysisStatus.IDLE);
  const [analysisResult, setAnalysisResult] = useState<RealEstateAnalysisResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [adminPassword, setAdminPassword] = useState<string>('');

  const handleStartAnalysis = async () => {
    if (!adminPassword.trim()) {
      setError('请输入管理员密码');
      return;
    }

    try {
      setAnalysisStatus(AnalysisStatus.ANALYZING);
      setError(null);
      setAnalysisResult(null);

      const isNetworkSafe = await checkNetworkSecurity();
      if (!isNetworkSafe) {
        setError('当前网络受限制，请切换网络后重试');
        setAnalysisStatus(AnalysisStatus.ERROR);
        return;
      }

      const result = await aiApi.analyzeRealEstate({
        admin_password: adminPassword,
        timeout_seconds: 300
      });

      setAnalysisResult(result);
      setAnalysisStatus(AnalysisStatus.COMPLETED);

    } catch (error) {
      let errorMessage = '分析失败，请重试';
      
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        if (axiosError.response?.data?.message) {
          errorMessage = axiosError.response.data.message;
        }
      }
      
      setError(errorMessage);
      setAnalysisStatus(AnalysisStatus.ERROR);
    }
  };

  const getStatusIcon = () => {
    switch (analysisStatus) {
      case AnalysisStatus.ANALYZING:
        return <Clock className="h-5 w-5 text-blue-400 animate-spin" />;
      case AnalysisStatus.COMPLETED:
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case AnalysisStatus.ERROR:
        return <AlertTriangle className="h-5 w-5 text-red-400" />;
      default:
        return <Building2 className="h-5 w-5 text-orange-400" />;
    }
  };

  const getStatusText = () => {
    switch (analysisStatus) {
      case AnalysisStatus.ANALYZING:
        return '正在分析数据库中的视频...';
      case AnalysisStatus.COMPLETED:
        return '分析完成';
      case AnalysisStatus.ERROR:
        return '分析失败';
      default:
        return '房地产视频分析';
    }
  };

  return (
    <Card variant="glass">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-orange-500/20 rounded-xl">
            {getStatusIcon()}
          </div>
          <div>
            <h3 className={`text-lg font-semibold transition-colors duration-300 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              {getStatusText()}
            </h3>
            <p className={`text-sm transition-colors duration-300 ${
              theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
            }`}>
              使用AI分析数据库中的待处理视频（需要管理员权限）
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <input
            type="password"
            value={adminPassword}
            onChange={(e) => setAdminPassword(e.target.value)}
            placeholder="请输入管理员密码"
            className={`px-3 py-2 rounded-lg border text-sm transition-colors duration-300 ${
              theme === 'dark'
                ? 'bg-dark-800 border-dark-600 text-white placeholder-dark-400'
                : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
            }`}
            disabled={analysisStatus === AnalysisStatus.ANALYZING}
          />
          <Button
            onClick={handleStartAnalysis}
            disabled={analysisStatus === AnalysisStatus.ANALYZING || !adminPassword.trim()}
            className="bg-orange-500 hover:bg-orange-600 flex items-center space-x-2"
          >
            <Play className="h-4 w-4" />
            <span>{analysisStatus === AnalysisStatus.ANALYZING ? '分析中...' : '开始分析'}</span>
          </Button>
        </div>
      </div>

      {error && (
        <div className={`p-4 rounded-lg mb-4 ${
          theme === 'dark' ? 'bg-red-900/20 border border-red-700/30' : 'bg-red-50 border border-red-200'
        }`}>
          <div className="flex items-start space-x-3">
            <AlertTriangle className={`h-5 w-5 mt-0.5 ${
              theme === 'dark' ? 'text-red-400' : 'text-red-600'
            }`} />
            <div>
              <h4 className={`font-medium ${
                theme === 'dark' ? 'text-red-400' : 'text-red-800'
              }`}>
                分析失败
              </h4>
              <p className={`text-sm mt-1 ${
                theme === 'dark' ? 'text-red-300' : 'text-red-700'
              }`}>
                {error}
              </p>
            </div>
          </div>
        </div>
      )}

      {analysisResult && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className={`p-4 rounded-lg ${
              theme === 'dark' ? 'bg-dark-800/50' : 'bg-gray-50'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp className="h-4 w-4 text-blue-400" />
                <span className={`text-sm font-medium ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  总分析数
                </span>
              </div>
              <div className={`text-2xl font-bold ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {analysisResult.total_analyzed}
              </div>
            </div>

            <div className={`p-4 rounded-lg ${
              theme === 'dark' ? 'bg-dark-800/50' : 'bg-gray-50'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                <Building2 className="h-4 w-4 text-green-400" />
                <span className={`text-sm font-medium ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  房地产相关
                </span>
              </div>
              <div className={`text-2xl font-bold ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {analysisResult.real_estate_count}
              </div>
            </div>

            <div className={`p-4 rounded-lg ${
              theme === 'dark' ? 'bg-dark-800/50' : 'bg-gray-50'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle className="h-4 w-4 text-purple-400" />
                <span className={`text-sm font-medium ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  成功率
                </span>
              </div>
              <div className={`text-2xl font-bold ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {(analysisResult.success_rate * 100).toFixed(1)}%
              </div>
            </div>

            <div className={`p-4 rounded-lg ${
              theme === 'dark' ? 'bg-dark-800/50' : 'bg-gray-50'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                <Clock className="h-4 w-4 text-orange-400" />
                <span className={`text-sm font-medium ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  处理时间
                </span>
              </div>
              <div className={`text-2xl font-bold ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {analysisResult.processing_time.toFixed(1)}s
              </div>
            </div>
          </div>

          {analysisResult.results.length > 0 && (
            <div className="mt-6">
              <h4 className={`text-md font-semibold mb-4 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                房地产相关视频 ({analysisResult.real_estate_count} 条)
              </h4>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {analysisResult.results
                  .filter(result => result.is_real_estate_related)
                  .slice(0, 10)
                  .map((result, index) => (
                    <div
                      key={result.aweme_id}
                      className={`p-4 rounded-lg border ${
                        theme === 'dark'
                          ? 'bg-dark-800/30 border-dark-600'
                          : 'bg-white border-gray-200'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <p className={`text-sm font-medium mb-1 ${
                            theme === 'dark' ? 'text-white' : 'text-gray-900'
                          }`}>
                            视频ID: {result.aweme_id}
                          </p>
                          <p className={`text-sm mb-2 ${
                            theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                          }`}>
                            {result.video_description.substring(0, 100)}...
                          </p>
                        </div>
                        <div className={`px-2 py-1 rounded text-xs font-medium ${
                          theme === 'dark' ? 'bg-green-900/30 text-green-400' : 'bg-green-100 text-green-600'
                        }`}>
                          {(result.confidence_score * 100).toFixed(0)}%
                        </div>
                      </div>
                      
                      {result.keywords_found.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {result.keywords_found.map((keyword, idx) => (
                            <span
                              key={idx}
                              className={`px-2 py-1 rounded-full text-xs ${
                                theme === 'dark' 
                                  ? 'bg-orange-900/30 text-orange-400' 
                                  : 'bg-orange-100 text-orange-600'
                              }`}
                            >
                              {keyword}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default RealEstateAnalysisComponent;
