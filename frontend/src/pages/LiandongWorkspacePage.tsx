import React, { useState, useEffect } from 'react';
import { ArrowLeft, Building2 } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import Button from '../components/Button';
import Card from '../components/Card';
import TopicBatchSearchComponent from '../components/TopicBatchSearchComponent.tsx';
import CommentAnalysisComponent from '../components/CommentAnalysisComponent';
import CustomerAnalysisComponent from '../components/CustomerAnalysisComponent';

import RealEstateAnalysisComponent from '../components/RealEstateAnalysisComponent';
import { MultiAccountManager, type DouyinAccount } from '../utils/multiAccount';
import type { CommentResult } from '../types';

const LiandongWorkspacePage: React.FC = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();

  const getThemeClasses = (lightClass: string, darkClass: string) =>
    theme === 'dark' ? darkClass : lightClass;


  const [commentResult, setCommentResult] = useState<CommentResult | null>(null);
  const [commentAccounts, setCommentAccounts] = useState<DouyinAccount[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<DouyinAccount | null>(null);

  useEffect(() => {
    const loadCommentAccounts = () => {
      const allAccounts = MultiAccountManager.getAccounts();
      const validAccounts = allAccounts.filter(account => account.isValid);
      setCommentAccounts(validAccounts);
      if (validAccounts.length > 0) {
        setSelectedAccount(validAccounts[0]);
      }
    };

    loadCommentAccounts();
  }, []);

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  const handleSearchComplete = (_results: any[]) => {
    // 搜索完成后的处理逻辑（如果需要的话）
  };

  const handleCommentAnalysisComplete = (result: CommentResult) => {
    setCommentResult(result);
  };

  return (
    <Layout>
      <div className="min-h-screen py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <Button
                onClick={handleBackToDashboard}
                className="bg-gray-500 hover:bg-gray-600 flex items-center space-x-2"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>返回仪表盘</span>
              </Button>
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-10 h-10 bg-orange-500 rounded-lg">
                  <Building2 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className={`text-3xl font-bold transition-colors duration-300 ${
                    getThemeClasses('text-gray-900', 'text-white')
                  }`}>
                    联东工作台
                  </h1>
                  <p className={`text-sm transition-colors duration-300 ${
                    getThemeClasses('text-gray-600', 'text-dark-300')
                  }`}>
                    批量话题搜索和数据分析
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-8">
            <RealEstateAnalysisComponent />
          </div>

          <TopicBatchSearchComponent
            onSearchComplete={handleSearchComplete}
          />

          <div className="mt-8">
            {commentAccounts.length > 0 && (
                      <Card variant="glass" className="mb-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className={`font-medium ${
                              theme === 'dark' ? 'text-white' : 'text-gray-900'
                            }`}>
                              评论分析账号
                            </h4>
                            <p className={`text-sm ${
                              theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                            }`}>
                              选择用于获取评论的账号
                            </p>
                          </div>
                          <select
                            value={selectedAccount?.id || ''}
                            onChange={(e) => {
                              const account = commentAccounts.find(acc => acc.id === e.target.value);
                              setSelectedAccount(account || null);
                            }}
                            className={`px-3 py-2 rounded-lg border text-sm ${
                              theme === 'dark'
                                ? 'bg-dark-800 border-dark-600 text-white'
                                : 'bg-white border-gray-300 text-gray-900'
                            }`}
                          >
                            {commentAccounts.map(account => (
                              <option key={account.id} value={account.id}>
                                {account.nickname}
                              </option>
                            ))}
                          </select>
                        </div>
              </Card>
            )}

            {selectedAccount ? (
              <CommentAnalysisComponent
                currentAccount={selectedAccount}
                onAnalysisComplete={handleCommentAnalysisComplete}
                showCommentDetails={false}
              />
            ) : (
              <Card variant="glass">
                <div className="text-center py-8">
                  <div className={`text-lg font-medium mb-2 transition-colors duration-300 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    暂无可用的评论账号
                  </div>
                  <p className={`text-sm transition-colors duration-300 ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    评论分析需要有效的抖音账号Cookie，请先在开发者工作台添加账号
                  </p>
                  <Button
                    onClick={() => navigate('/workspace/douyin/developer')}
                    className="mt-4 bg-purple-500 hover:bg-purple-600"
                  >
                    前往开发者工作台
                  </Button>
                </div>
              </Card>
            )}

            {commentResult && (
              <div className="mt-8">
                <CustomerAnalysisComponent
                  commentResult={commentResult}
                  showCustomerDetails={false}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default LiandongWorkspacePage;
